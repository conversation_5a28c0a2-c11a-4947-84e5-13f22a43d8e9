import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:async';
import '../constants/colors.dart';
import '../constants/images.dart';
import '../components/search_bar.dart';
import '../components/post_list.dart';
import '../components/category_bar.dart';
import '../api/recommendation_service.dart';
import '../models/post.dart';
import 'package:flutter/rendering.dart';
import '../screens/search_screen.dart';
import '../utils/location_preferences_utils.dart';
import '../utils/user_preferences_utils.dart';
import '../api/favorite_service.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  String activeTab = "nearMe";
  bool isLoading = true;
  bool isLoadingMore = false;
  bool isRefreshing = false;
  String? errorMessage;

  // Current location
  double? currentLat;
  double? currentLon;
  String? currentAddress;

  // StreamSubscription to track location
  StreamSubscription<Position>? _positionStreamSubscription;

  // Posts data with pagination
  List<Post> nearMePosts = [];
  List<Post> trendingPosts = [];
  List<Post> forYouPosts = [];

  Set<String> userFavorites = {};

  Map<String, int> currentPage = {
    "nearMe": 1,
    "trending": 1,
    "forYou": 1,
  };

  Map<String, bool> hasMoreData = {
    "nearMe": true,
    "trending": true,
    "forYou": true,
  };

  final ScrollController _scrollController = ScrollController();

  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  String? userName;

  late AnimationController _headerAnimationController;
  late Animation<double> _headerAnimation;
  bool _isHeaderCollapsed = false;

  @override
  void initState() {
    super.initState();
    _loadUserName();
    _setupLocationTracking();
    _setupLocationPermissions();
    _scrollController.addListener(_scrollListener);

    // Initialize animation controller for header
    _headerAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _headerAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _headerAnimationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _positionStreamSubscription?.cancel();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _headerAnimationController.dispose();
    super.dispose();
  }

  Future<void> _setupLocationTracking() async {
    // First try to load saved location for immediate display
    await _loadSavedLocation();

    // Then start continuous location tracking
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return;
      }

      // Request permission if not already granted
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        return;
      }

      // Start listening to location updates
      _positionStreamSubscription = Geolocator.getPositionStream(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10, // Update if moved 10 meters
        ),
      ).listen((Position position) async {
        final address =
            await LocationPreferencesUtils.getAddressFromCoordinates(
                position.latitude, position.longitude);

        setState(() {
          currentLat = position.latitude;
          currentLon = position.longitude;
          currentAddress = address;
        });

        await LocationPreferencesUtils.saveLocation(
            position.latitude, position.longitude, address);
      });
    } catch (e) {
      print("Error setting up location tracking: $e");
    }
  }

  Future<void> _loadSavedLocation() async {
    final locationData = await LocationPreferencesUtils.loadSavedLocation();
    print(locationData);

    // Set saved location data if available
    if (locationData['latitude'] != null &&
        locationData['longitude'] != null &&
        locationData['address'] != null &&
        locationData['address'].toString().isNotEmpty) {
      setState(() {
        currentLat = locationData['latitude'];
        currentLon = locationData['longitude'];
        currentAddress = locationData['address'];
      });
    }

    // Always try to get current location regardless of saved data
    await _updateLocationData();

    if (currentLat != null && currentLon != null) {
      _fetchInitialData();
    }
  }

  Future<void> _loadUserName() async {
    final userProfile = await UserPreferencesUtils.getUserProfile();
    print(userProfile);
    setState(() {
      userName = userProfile['name'] as String?;
    });
  }

  Future<void> _loadUserFavorites() async {
    try {
      final List<String> favorites = await FavoriteService.getUserFavorites();
      setState(() {
        userFavorites = Set<String>.from(favorites);
      });
    } catch (e) {
      print('Error loading user favorites: $e');
    }
  }

  void _scrollListener() {
    // Handle scroll for loading more data
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 500) {
      _loadMoreData();
    }

    // Handle scroll for collapsing/expanding header
    if (_scrollController.position.pixels > 150 && !_isHeaderCollapsed) {
      _headerAnimationController.forward();
      setState(() {
        _isHeaderCollapsed = true;
      });
    } else if (_scrollController.position.pixels <= 150 && _isHeaderCollapsed) {
      _headerAnimationController.reverse();
      setState(() {
        _isHeaderCollapsed = false;
      });
    }

    // Update CategoryBar animation based on scroll direction
    if (_scrollController.position.userScrollDirection ==
            ScrollDirection.reverse ||
        _scrollController.position.userScrollDirection ==
            ScrollDirection.forward) {
      _categoryBarKey.currentState
          ?.updateExpansion(_scrollController.position.userScrollDirection);
    }
  }

  final GlobalKey<CategoryBarState> _categoryBarKey =
      GlobalKey<CategoryBarState>();

  void _handleCategoryTap(String searchQuery) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SearchScreen(initialQuery: searchQuery),
      ),
    );
  }

  Future<void> _loadMoreData() async {
    if (isLoadingMore || !hasMoreData[activeTab]!) return;

    setState(() {
      isLoadingMore = true;
    });

    try {
      List<Post> newPosts = [];
      currentPage[activeTab] = currentPage[activeTab]! + 1;

      if (activeTab == "nearMe") {
        newPosts = await RecommendationService.fetchNearbyRecommendations(
          lat: currentLat!,
          lon: currentLon!,
          page: currentPage[activeTab]!,
        );
        if (newPosts.isNotEmpty) {
          // Mark new posts as favorites if they are in the user's favorites list
          newPosts = _markFavoritePosts(newPosts);
          setState(() {
            nearMePosts.addAll(newPosts);
          });
        }
      } else if (activeTab == "trending") {
        newPosts = await RecommendationService.fetchTrendingRecommendations(
          lat: currentLat!,
          lon: currentLon!,
          page: currentPage[activeTab]!,
        );
        if (newPosts.isNotEmpty) {
          // Mark new posts as favorites if they are in the user's favorites list
          newPosts = _markFavoritePosts(newPosts);
          setState(() {
            trendingPosts.addAll(newPosts);
          });
        }
      } else if (activeTab == "forYou") {
        newPosts = await RecommendationService.fetchHybridRecommendations(
          lat: currentLat!,
          lon: currentLon!,
          page: currentPage[activeTab]!,
        );
        if (newPosts.isNotEmpty) {
          newPosts = _markFavoritePosts(newPosts);
          setState(() {
            forYouPosts.addAll(newPosts);
          });
        }
      }

      setState(() {
        hasMoreData[activeTab] = newPosts.isNotEmpty && newPosts.length >= 10;
        isLoadingMore = false;
      });
    } catch (e) {
      setState(() {
        isLoadingMore = false;
      });
      print("Error loading more data: $e");
    }
  }

  Future<void> _setupLocationPermissions() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          setState(() {
            errorMessage = "Location permission denied";
            isLoading = false;
          });
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        setState(() {
          errorMessage =
              "Location permission permanently denied. Please enable in settings.";
          isLoading = false;
        });
        return;
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          errorMessage = "Unable to get location permission: $e";
          isLoading = false;
        });
      }
    }
  }

  Future<void> _updateLocationData() async {
    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      ).timeout(const Duration(seconds: 10), onTimeout: () {
        throw TimeoutException("Location request timed out");
      });

      if (mounted) {
        // Use our utility to get detailed address
        final address =
            await LocationPreferencesUtils.getAddressFromCoordinates(
                position.latitude, position.longitude);

        setState(() {
          currentLat = position.latitude;
          currentLon = position.longitude;
          currentAddress = address;
        });

        await LocationPreferencesUtils.saveLocation(
            position.latitude, position.longitude, address);
      }
    } catch (e) {
      print("Error updating location: $e");

      if (currentLat == null || currentLon == null) {
        setState(() {
          errorMessage = "Unable to get location: $e";
          isLoading = false;
        });
      }
    }
  }

  // Pull-to-refresh implementation
  Future<void> _onRefresh() async {
    if (isRefreshing) return;

    setState(() {
      isRefreshing = true;
    });

    try {
      // Always update location first regardless of tab content
      await _updateLocationData();

      await _loadUserFavorites();

      // Reset pagination and clear existing data
      await _resetAndRefetch();

      setState(() {
        isRefreshing = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = "Error refreshing data: $e";
        isRefreshing = false;
      });
    }
  }

  Future<void> _resetAndRefetch() async {
    setState(() {
      currentPage = {
        "nearMe": 1,
        "trending": 1,
        "forYou": 1,
      };
      hasMoreData = {
        "nearMe": true,
        "trending": true,
        "forYou": true,
      };
      nearMePosts = [];
      trendingPosts = [];
      forYouPosts = [];
    });

    await _fetchInitialData();
  }

  Future<void> _fetchInitialData() async {
    if (currentLat == null || currentLon == null) {
      return;
    }

    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      await _loadUserFavorites();

      // Fetch initial data for each tab with pagination
      final results = await Future.wait([
        RecommendationService.fetchNearbyRecommendations(
          lat: currentLat!,
          lon: currentLon!,
          page: 1,
          limit: 10,
        ),
        RecommendationService.fetchTrendingRecommendations(
          lat: currentLat!,
          lon: currentLon!,
          page: 1,
          limit: 10,
        ),
        RecommendationService.fetchHybridRecommendations(
          lat: currentLat!,
          lon: currentLon!,
          page: 1,
          limit: 10,
        ),
      ]).timeout(const Duration(seconds: 20), onTimeout: () {
        throw TimeoutException(
            "Initial data requests timed out after 20 seconds");
      });

      if (mounted) {
        setState(() {
          nearMePosts = _markFavoritePosts(results[0]);
          trendingPosts = _markFavoritePosts(results[1]);
          forYouPosts = _markFavoritePosts(results[2]);

          // Update hasMoreData flags
          hasMoreData["nearMe"] = results[0].length >= 10;
          hasMoreData["trending"] = results[1].length >= 10;
          hasMoreData["forYou"] = results[2].length >= 10;

          isLoading = false;
        });
      }
    } catch (e) {
      // Only update state if the widget is still mounted
      if (mounted) {
        setState(() {
          errorMessage = "Unable to load data: $e";
          isLoading = false;
        });
      }
    }
  }

  List<Post> _markFavoritePosts(List<Post> posts) {
    for (var post in posts) {
      post.isFavorite = userFavorites.contains(post.id);
    }
    return posts;
  }

  void _handleFavoriteToggle(Post post, bool isFavorite) {
    setState(() {
      // Update the favorite status in the data
      post.isFavorite = isFavorite;

      // Update the userFavorites set
      if (isFavorite) {
        userFavorites.add(post.id);
      } else {
        userFavorites.remove(post.id);
      }
    });
  }

  Widget renderTabContent() {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              errorMessage!,
              style: TextStyle(color: AppColors.redPrimary),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _fetchInitialData,
              child: const Text("Try Again"),
            ),
          ],
        ),
      );
    }

    // Get current posts based on active tab
    List<Post> currentPosts = [];
    switch (activeTab) {
      case "nearMe":
        currentPosts = nearMePosts;
        break;
      case "trending":
        currentPosts = trendingPosts;
        break;
      case "forYou":
        currentPosts = forYouPosts;
        break;
    }

    if (currentPosts.isEmpty) {
      return RefreshIndicator(
        key: _refreshIndicatorKey,
        onRefresh: _onRefresh,
        child: ListView(
          children: [
            SizedBox(
              height: MediaQuery.of(context).size.height / 2,
              child: Center(
                child: Text(
                  "No posts found. Pull down to refresh.",
                  style: TextStyle(color: Gray.text),
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Stack(
      children: [
        RefreshIndicator(
          key: _refreshIndicatorKey,
          onRefresh: _onRefresh,
          child: NotificationListener<ScrollNotification>(
            onNotification: (ScrollNotification scrollInfo) {
              if (scrollInfo.metrics.pixels >=
                  scrollInfo.metrics.maxScrollExtent - 500) {
                _loadMoreData();
              }
              return true;
            },
            child: PostList(
              type: activeTab,
              data: currentPosts,
              scrollController: _scrollController,
              onFavoriteToggled: _handleFavoriteToggle,
            ),
          ),
        ),

        // Show loading indicator at the bottom if loading more
        if (isLoadingMore && hasMoreData[activeTab]!)
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              color: Colors.white.withOpacity(0.8),
              padding: const EdgeInsets.all(8.0),
              alignment: Alignment.center,
              child: const CircularProgressIndicator(),
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Collapsible Header
            AnimatedBuilder(
              animation: _headerAnimation,
              builder: (context, child) {
                return Column(
                  children: [
                    // Main Header (Logo and User Info)
                    Container(
                      color: AppColors.redPrimary,
                      padding: const EdgeInsets.fromLTRB(16, 16, 16, 16),
                      child: Row(
                        children: [
                          // Logo
                          Container(
                            margin: const EdgeInsets.only(right: 10),
                            child: Image.asset(
                              AppImages.logo,
                              width: 40,
                              height: 40,
                              color: Colors.white,
                            ),
                          ),
                          // User Info - always visible
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Welcome',
                                  style: TextStyle(
                                    color: AppColors.white,
                                    fontSize: 24,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  userName ?? 'Loading...',
                                  style: TextStyle(
                                    color: AppColors.white,
                                    fontSize: 20,
                                    fontWeight: FontWeight.w800,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Collapsible location row
                    ClipRect(
                      child: Align(
                        heightFactor: _headerAnimation.value,
                        child: Container(
                          color: AppColors.redPrimary,
                          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                          child: InkWell(
                            onTap: () {
                              // Manually trigger refresh indicator
                              _refreshIndicatorKey.currentState?.show();
                            },
                            child: Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    'Location: ${currentAddress ?? 'Getting location...'}',
                                    style: TextStyle(
                                      color: AppColors.white,
                                      fontSize: 12,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Icon(
                                  Icons.refresh,
                                  color: AppColors.white,
                                  size: 16,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),

            // Search Bar
            const SearchBarPost(),

            // Using our new CategoryBar component
            CategoryBar(
              key: _categoryBarKey,
              onCategoryTap: _handleCategoryTap,
            ),

            // Tab Navigation
            Container(
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: Gray.border,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildTabButton(
                      'Gần tôi', 'nearMe', MediaQuery.of(context).size.width),
                  _buildTabButton('Đang hot', 'trending',
                      MediaQuery.of(context).size.width),
                  _buildTabButton('Dành cho bạn', 'forYou',
                      MediaQuery.of(context).size.width),
                ],
              ),
            ),

            // Indicator for refreshing status
            if (isRefreshing && !isLoading)
              Container(
                padding: const EdgeInsets.symmetric(vertical: 8),
                color: Colors.blue.withOpacity(0.1),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor:
                            AlwaysStoppedAnimation<Color>(AppColors.primary),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Refreshing location and content...',
                      style: TextStyle(
                        color: AppColors.primary,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),

            // Tab Content - Scrollable
            Expanded(
              child: renderTabContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabButton(String label, String tabId, double windowWidth) {
    bool isActive = activeTab == tabId;

    return InkWell(
      onTap: () {
        setState(() {
          activeTab = tabId;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        alignment: Alignment.center,
        width: windowWidth / 3,
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: isActive ? AppColors.primary : Colors.transparent,
              width: 2,
            ),
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isActive ? AppColors.primary : Gray.text,
            fontWeight: isActive ? FontWeight.w600 : FontWeight.w500,
            fontSize: 14,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
