import 'package:flutter/material.dart';
import '../constants/colors.dart';
import '../models/post.dart';
import '../components/post_list.dart';
import '../api/favorite_service.dart';
import '../utils/api_utils.dart';
import '../api/config.dart';
import 'dart:convert';

class CollectionsScreen extends StatefulWidget {
  const CollectionsScreen({Key? key}) : super(key: key);
  @override
  State<CollectionsScreen> createState() => _CollectionsScreenState();
}

class _CollectionsScreenState extends State<CollectionsScreen> {
  List<Post> _favoritePosts = [];
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadFavoritePosts();
  }

  Future<void> _loadFavoritePosts() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
    });
    try {
      final favoritePostsData = await FavoriteService.getUserFavoritesWithDetails();

      if (favoritePostsData.isNotEmpty) {
        // Convert API data directly to Post objects
        List<Post> posts = [];
        for (var postData in favoritePostsData) {
          // Safely convert rate to double before multiplication
          double rateValue = 0.0;
          if (postData['rate'] != null) {
            if (postData['rate'] is int) {
              rateValue = (postData['rate'] as int).toDouble();
            } else if (postData['rate'] is double) {
              rateValue = postData['rate'];
            } else {
              rateValue = double.tryParse(postData['rate'].toString()) ?? 0.0;
            }
          }

          Post post = Post(
            id: postData['id'] ?? '',
            title: _extractTitle(postData['message'] ?? ''),
            image: postData['full_picture'] ?? '',
            location: postData['address'] ?? 'Unknown location',
            rating: rateValue, // Now using safely converted double
            comments: 0, // Not available in the API response
            reactions: postData['reactions_total_count'] ?? 0,
            isFavorite: true, // Since these are favorites
          );
          posts.add(post);
        }

        setState(() {
          _favoritePosts = posts;
          _isLoading = false;
        });
      } else {
        setState(() {
          _favoritePosts = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Failed to load your collections: ${e.toString()}';
      });
    }
  }

  // Helper function to extract a title from the message
  String _extractTitle(String message) {
    if (message.isEmpty) return 'Untitled';

    // Try to get the first line or first sentence
    var firstLine = message.split('\n').first.trim();
    if (firstLine.length > 50) {
      // If first line is too long, get just the first sentence or part
      var firstSentence = message.split('.').first.trim();
      return firstSentence.length > 50 ? firstSentence.substring(0, 50) + '...' : firstSentence;
    }
    return firstLine;
  }

  void _handleFavoriteToggled(Post post, bool isFavorite) {
    // If post is removed from favorites, remove it from our list
    if (!isFavorite) {
      setState(() {
        _favoritePosts.removeWhere((item) => item.id == post.id);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.only(top: 30, bottom: 16),
              alignment: Alignment.center,
              child: Text(
                'Collections',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.black,
                ),
              ),
            ),
            // Main content area
            Expanded(
              child: _buildContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }
    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _errorMessage,
              style: TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadFavoritePosts,
              child: const Text('Try Again'),
            ),
          ],
        ),
      );
    }
    if (_favoritePosts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.favorite_border,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No saved places yet',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Items you save will appear here',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }
    // Display the favorite posts using the PostList component
    return RefreshIndicator(
      onRefresh: _loadFavoritePosts,
      child: PostList(
        type: "collections",
        data: _favoritePosts,
        onFavoriteToggled: _handleFavoriteToggled,
      ),
    );
  }
}