class Comment {
  final String id;
  final String content;
  final String userName;
  final String userAvatar;
  final String date;
  final String? postId;
  final String? postTitle;

  Comment({
    required this.id,
    required this.content,
    required this.userName,
    required this.userAvatar,
    required this.date,
    this.postId,
    this.postTitle,
  });

  factory Comment.fromJson(Map<String, dynamic> json) {
    return Comment(
      id: json['id'] ?? '',
      content: json['content'] ?? '',
      userName: json['user_name'] ?? '',
      userAvatar: json['user_avatar'] ?? 'assets/images/default_avatar.png',
      date: json['date'] ?? '',
      postId: json['post_id'],
      postTitle: json['post_title'],
    );
  }
}