// src/services/crawlerService.js
import axios from 'axios';

const CRAWLER_API = 'http://localhost:5000/api/v1';

export const crawlData = async () => {
  try {
    const response = await axios.get(`${CRAWLER_API}/scrape`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const stopCrawling = async () => {
  try {
    const response = await axios.post(`${CRAWLER_API}/stop-crawl`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

// src/services/dataService.js
import axios from 'axios';

const POST_API = 'http://localhost:8080/api/post';
const UPDATE_API = 'http://localhost:5000/api/v1';
const PREPROCESS_API = 'http://localhost:5005/api/v1';
const TAG_API = 'http://localhost:5010/api/v1';
const POSITION_API = 'http://localhost:5015/api/v1';

export const getAllPosts = async () => {
  try {
    const response = await axios.get(`${POST_API}/getAll`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const updateDataPipeline = async (updateCallback) => {
  try {
    // Step 1: Update data
    updateCallback(1, 20);
    const updateResponse = await axios.put(`${UPDATE_API}/update`);
    
    // Step 2: Preprocess data
    updateCallback(2, 40);
    const preprocessResponse = await axios.post(`${PREPROCESS_API}/preprocess`);
    
    // Step 3: Summarize data
    updateCallback(3, 60);
    const summarizeResponse = await axios.post(`${PREPROCESS_API}/summarize`);
    
    // Step 4: Tag data
    updateCallback(4, 80);
    const tagResponse = await axios.post(`${TAG_API}/tag-data`);
    
    // Step 5: Tag position
    updateCallback(5, 100);
    const positionResponse = await axios.get(`${POSITION_API}/tag-position`);
    
    return {
      update: updateResponse.data,
      preprocess: preprocessResponse.data,
      summarize: summarizeResponse.data,
      tag: tagResponse.data,
      position: positionResponse.data
    };
  } catch (error) {
    throw error;
  }
};