import React, { useState, useEffect } from "react";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import ProgressBar from "./ProgressBar";
import {
  updateData,
  preprocessData,
  summarizeData,
  tagData,
  tagPosition,
} from "../services/api";
import { useAppContext } from "../context/AppContext";

const DataUpdater = () => {
  const {
    isUpdating, setIsUpdating,
    updateProgress, setUpdateProgress,
    updateStatus, setUpdateStatus,
    updateError, setUpdateError,
    updateSteps, setUpdateSteps,
    updateStepStatus,
    calculateProgress,
    resetUpdateProcess,
  } = useAppContext();

  const [cookie, setCookie] = useState(
    () => localStorage.getItem("facebookCookie") || ""
  );
  const [tempCookie, setTempCookie] = useState(
    () => localStorage.getItem("facebookCookie") || ""
  );

  useEffect(() => {
    // No need to reset update process on component mount
    // Only initialize cookie state
    if (!cookie) {
      setCookie(localStorage.getItem("facebookCookie") || "");
      setTempCookie(localStorage.getItem("facebookCookie") || "");
    }
  }, [cookie]);

  const handleCookieSubmit = (e) => {
    e.preventDefault();
    if (tempCookie.trim() === "") {
      toast.error("Cookie không được để trống", {
        position: "top-right",
        autoClose: 3000,
      });
      return;
    }
    localStorage.setItem("facebookCookie", tempCookie);
    setCookie(tempCookie);
    toast.success("Cookie đã được lưu thành công!", {
      position: "top-right",
      autoClose: 3000,
    });
  };

  const handleUpdateData = async () => {
    if (!cookie) {
      toast.error("Vui lòng nhập cookie Facebook trước khi cập nhật dữ liệu", {
        position: "top-right",
        autoClose: 3000,
      });
      return;
    }

    try {
      setIsUpdating(true);
      resetUpdateProcess();
      setUpdateStatus("Đang cập nhật dữ liệu...");
      let steps = [...updateSteps];
      
      // Step 1: Update Data
      updateStepStatus(1, "processing");
      setUpdateStatus("Đang cập nhật dữ liệu...");
      await updateData(cookie);
      updateStepStatus(1, "completed", "Cập nhật dữ liệu thành công");
      steps[0].status = "completed";
      setUpdateProgress(calculateProgress(steps));

      // Step 2: Preprocess
      updateStepStatus(2, "processing");
      setUpdateStatus("Đang tiền xử lý dữ liệu...");
      await preprocessData();
      updateStepStatus(2, "completed", "Tiền xử lý thành công");
      steps[1].status = "completed";
      setUpdateProgress(calculateProgress(steps));

      // Step 3: Summarize
      updateStepStatus(3, "processing");
      setUpdateStatus("Đang tóm tắt dữ liệu...");
      await summarizeData();
      updateStepStatus(3, "completed", "Tóm tắt thành công");
      steps[2].status = "completed";
      setUpdateProgress(calculateProgress(steps));

      // Step 4: Tag Data
      updateStepStatus(4, "processing");
      setUpdateStatus("Đang gắn thẻ dữ liệu...");
      await tagData();
      updateStepStatus(4, "completed", "Gắn thẻ dữ liệu thành công");
      steps[3].status = "completed";
      setUpdateProgress(calculateProgress(steps));

      // Step 5: Tag Position
      updateStepStatus(5, "processing");
      setUpdateStatus("Đang gắn vị trí thẻ...");
      await tagPosition();
      updateStepStatus(5, "completed", "Gắn vị trí thẻ thành công");
      steps[4].status = "completed";
      setUpdateProgress(calculateProgress(steps));

      setUpdateStatus("Cập nhật dữ liệu thành công!");
      setUpdateProgress(100);
      toast.success("Tất cả các bước đã hoàn thành thành công!", {
        position: "top-right",
        autoClose: 5000,
      });
    } catch (err) {
      const errorMessage = "Lỗi khi cập nhật dữ liệu: " + err.message;
      setUpdateError(errorMessage);
      setUpdateStatus("Cập nhật dữ liệu thất bại");
      setIsUpdating(false);
      setUpdateProgress(0);
      setUpdateSteps((steps) =>
        steps.map((step) =>
          step.status === "processing"
            ? { ...step, status: "failed", message: errorMessage }
            : step
        )
      );
      toast.error(errorMessage, {
        position: "top-right",
        autoClose: 5000,
      });
      return;
    }
    setIsUpdating(false);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case "completed":
        return (
          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-green-500">
            <svg
              className="w-4 h-4 text-white"
              fill="none"
              stroke="currentColor"
              strokeWidth="3"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M5 13l4 4L19 7"
              />
            </svg>
          </span>
        );
      case "processing":
        return (
          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-yellow-400">
            <svg
              className="w-4 h-4 text-white animate-spin"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              />
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>
          </span>
        );
      case "failed":
        return (
          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-red-500">
            <svg
              className="w-4 h-4 text-white"
              fill="none"
              stroke="currentColor"
              strokeWidth="3"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-gray-200 text-gray-500 font-bold">
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              viewBox="0 0 24 24"
            >
              <circle cx="12" cy="12" r="10" />
            </svg>
          </span>
        );
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <ToastContainer />
      {/* Cookie Section */}
      <div className="bg-white rounded-xl shadow-lg p-6 mb-8 border border-gray-100">
        <form onSubmit={handleCookieSubmit} className="mb-2">
          <div className="relative mb-4">
            <textarea
              id="cookie"
              className="w-full p-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 min-h-24 transition"
              value={tempCookie}
              onChange={(e) => setTempCookie(e.target.value)}
              placeholder="Nhập cookie Facebook của bạn"
            />
          </div>
          <button
            type="submit"
            className="bg-indigo-700 text-white rounded-lg px-2 py-2"
          >
            Lưu Cookie
          </button>
        </form>
        {cookie && (
          <div className="mt-2 text-green-600 flex items-center">
            <svg
              className="w-5 h-5 mr-1"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clipRule="evenodd"
              />
            </svg>
            Cookie đã được lưu
          </div>
        )}
      </div>

      {/* Status & Progress Section */}
      <div className="bg-white rounded-xl shadow-lg p-6 mb-8 border border-gray-100">
        <h2 className="text-xl font-semibold mb-4 text-indigo-600 flex items-center">
          Tiến trình cập nhật
        </h2>
        {/* <div className="mb-4">
          <ProgressBar progress={updateProgress} />
          <div className="text-right text-sm text-gray-600 mt-1"></div>
        </div> */}
        <div className="mb-4 text-gray-700">
          <span className="font-medium">Trạng thái:&nbsp;</span>
          <span
            className={
              isUpdating
                ? "text-yellow-500 font-semibold"
                : updateProgress === 100
                ? "text-green-500 font-semibold"
                : "text-gray-500"
            }
          >
            {updateStatus}
          </span>
        </div>
        <div className="flex space-x-4">
          <button
            onClick={handleUpdateData}
            disabled={isUpdating}
            className={`flex items-center px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
              isUpdating
                ? "bg-gray-300 cursor-not-allowed"
                : "bg-indigo-700 shadow-md hover:shadow-lg text-white"
            }`}
          >
            {isUpdating ? (
              <>
                <svg
                  className="w-5 h-5 mr-2 animate-spin"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  />
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
                Đang cập nhật...
              </>
            ) : (
              <>
                <svg
                  className="w-5 h-5 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M5 13l4 4L19 7"
                  />
                </svg>
                Cập nhật dữ liệu
              </>
            )}
          </button>
        </div>
        {updateError && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mt-4">
            {updateError}
          </div>
        )}
      </div>

      {/* Steps Section */}
      <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
        <h2 className="text-xl font-semibold mb-4 text-indigo-600 flex items-center">
          Quá trình cập nhật
        </h2>
        <ul className="space-y-4">
          {updateSteps.map((step) => (
            <li key={step.id} className="flex items-center">
              <div className="mr-3">{getStatusIcon(step.status)}</div>
              <div>
                <span
                  className={`font-medium ${
                    step.status === "completed"
                      ? "text-green-600"
                      : step.status === "processing"
                      ? "text-yellow-500"
                      : step.status === "failed"
                      ? "text-red-500"
                      : "text-gray-600"
                  }`}
                >
                  {step.name}
                </span>
                {step.message && (
                  <div className="text-sm text-gray-500 mt-1">
                    {step.message}
                  </div>
                )}
              </div>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default DataUpdater;