from flask import Flask, Blueprint, jsonify, request
import os
from dotenv import load_dotenv
from config import get_mysql_config
from database import DatabaseManager
from preprocess import PreprocessManager
import threading
from queue import Queue
import uuid
from datetime import datetime
from typing import Dict
from flask_cors import CORS 

load_dotenv()

# Initialize Flask app
app = Flask(__name__)
CORS(app) 
MYSQL_CONFIG = get_mysql_config()
db_manager = DatabaseManager(MYSQL_CONFIG)
preprocess_manager = PreprocessManager(db_manager)

api_v1 = Blueprint('api_v1', __name__, url_prefix='/api/v1')

summarize_thread = None
stop_summarize_event = threading.Event()  # Renamed to avoid conflict

@api_v1.route('/preprocess', methods=['POST'])
def preprocess():
    try:
        # Execute both delete_empty_messages and fix_messages
        delete_success, delete_result = preprocess_manager.delete_messages()
        fix_success, fix_result = preprocess_manager.fix_messages()

        if not delete_success or not fix_success:
            return jsonify({
                "status": "error",
                "message": "An error occurred during preprocessing",
                "details": {
                    "delete_messages": delete_result,
                    "fix_messages": fix_result
                }
            }), 500

        return jsonify({
            "status": "success",
            "message": "Preprocessing completed successfully",
            "details": {
                "delete_messages": delete_result,
                "fix_messages": fix_result
            }
        }), 200

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@api_v1.route('/summarize', methods=['POST'])
def summarize():
    global summarize_thread, stop_summarize_event
    
    try:
        print("Start summarize")
        
        if summarize_thread and summarize_thread.is_alive():
            return jsonify({
                "status": "error",
                "message": "Summarize is already in progress."
            }), 400

        stop_summarize_event.clear()

        success, result = preprocess_manager.find_and_merge_similar_posts()

        return jsonify({
            "status": "success" if success else "error",
            "message": "Summarization completed" if success else "Summarization failed",
            "result": result
        }), 200 if success else 500

    except Exception as e:
        print("Error during summarization:", str(e))
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@api_v1.route('/summarize/stop', methods=['POST'])
def stop_summarize():
    global summarize_thread, stop_summarize_event
    
    try:
        if summarize_thread and summarize_thread.is_alive():
            stop_summarize_event.set()
            summarize_thread.join(timeout=5)

            return jsonify({
                "status": "success",
                "message": "Summarize process stopped successfully."
            }), 200
        else:
            return jsonify({
                "status": "error",
                "message": "No summarize process is currently running."
            }), 400
            
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Error stopping summarization: {str(e)}"
        }), 500

# Register Blueprint
app.register_blueprint(api_v1)

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=5005, debug=True)