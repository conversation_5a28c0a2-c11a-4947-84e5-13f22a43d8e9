// lib/widgets/post_detail/image_slider.dart
import 'package:flutter/material.dart';
import 'package:funplay/models/post_detail.dart';
import 'package:funplay/constants/images.dart';
import 'package:http/http.dart' as http;

class ImageSlider extends StatefulWidget {
  final PostDetail postDetail;

  const ImageSlider({Key? key, required this.postDetail}) : super(key: key);

  @override
  _ImageSliderState createState() => _ImageSliderState();
}

class _ImageSliderState extends State<ImageSlider> {
  int currentImageIndex = 0;
  List<String> validImageUrls = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _validateImages();
  }

  Future<void> _validateImages() async {
    // Get image URLs from the post detail
    final List<String> imageUrls = widget.postDetail.getImageUrls();
    List<String> tempValidUrls = [];

    // Validate each URL (only for network images)
    for (String url in imageUrls) {
      if (url.startsWith('http')) {
        try {
          final response = await http.head(Uri.parse(url));
          if (response.statusCode != 403) {
            tempValidUrls.add(url);
          } else {
            print("Skipping image with 403 error: $url");
          }
        } catch (e) {
          print("Error checking image URL: $e");
          // Skip this URL on any error
        }
      } else {
        // Asset images don't need validation, add directly
        tempValidUrls.add(url);
      }
    }

    // If all images are invalid, add a placeholder
    if (tempValidUrls.isEmpty) {
      tempValidUrls.add(AppImages.postDetailImage);
    }

    setState(() {
      validImageUrls = tempValidUrls;
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Container(
        height: 250,
        width: double.infinity,
        color: Colors.grey[200],
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Container(
      height: 250,
      width: double.infinity,
      child: Stack(
        children: [
          // Current image
          _buildImage(validImageUrls[currentImageIndex]),

          // Only show navigation if there's more than one image
          if (validImageUrls.length > 1)
            Positioned(
              top: 0,
              bottom: 0,
              left: 0,
              right: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Previous button
                  GestureDetector(
                    onTap: () => _prevImage(validImageUrls.length),
                    child: Container(
                      margin: const EdgeInsets.only(left: 10),
                      width: 30,
                      height: 30,
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: const Center(
                        child: Icon(
                          Icons.chevron_left,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),

                  // Next button
                  GestureDetector(
                    onTap: () => _nextImage(validImageUrls.length),
                    child: Container(
                      margin: const EdgeInsets.only(right: 10),
                      width: 30,
                      height: 30,
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: const Center(
                        child: Icon(
                          Icons.chevron_right,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

          // Image indicator dots (only show if more than one image)
          if (validImageUrls.length > 1)
            Positioned(
              bottom: 10,
              left: 0,
              right: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  validImageUrls.length,
                      (index) => Container(
                    width: 8,
                    height: 8,
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: currentImageIndex == index
                          ? Colors.white
                          : Colors.white.withOpacity(0.5),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _nextImage(int totalImages) {
    setState(() {
      if (currentImageIndex == totalImages - 1) {
        currentImageIndex = 0;
      } else {
        currentImageIndex++;
      }
    });
  }

  void _prevImage(int totalImages) {
    setState(() {
      if (currentImageIndex == 0) {
        currentImageIndex = totalImages - 1;
      } else {
        currentImageIndex--;
      }
    });
  }

  // Helper method to build image from either asset or network source
  Widget _buildImage(String imageSource) {
    if (imageSource.startsWith('http')) {
      return Image.network(
        imageSource,
        width: double.infinity,
        height: 250,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          print("Error loading network image: $error");
          return _buildFallbackImage();
        },
      );
    } else {
      return Image.asset(
        imageSource,
        width: double.infinity,
        height: 250,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          print("Error loading asset image: $error");
          return _buildFallbackImage();
        },
      );
    }
  }

  Widget _buildFallbackImage() {
    return Container(
      width: double.infinity,
      height: 250,
      color: Colors.grey[300],
      child: Icon(Icons.image_not_supported, color: Colors.grey[600], size: 40),
    );
  }
}