import 'package:flutter/material.dart';
import 'package:funplay/utils/user_preferences_utils.dart';
import 'package:provider/provider.dart';
import '../api/profile.dart';
import '../providers/auth_provider.dart';
import 'package:intl/intl.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  _ProfileScreenState createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  Map<String, dynamic> userData = {};
  bool isLoading = true;

  String editField = "";
  String editValue = "";
  bool modalVisible = false;
  bool isUpdating = false;
  bool isLoggingOut = false;

  final _formKey = GlobalKey<FormState>();
  final TextEditingController _editController = TextEditingController();
  final DateFormat _dateFormat = DateFormat('yyyy-MM-dd');

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }

  @override
  void dispose() {
    _editController.dispose();
    super.dispose();
  }

  // Helper method to format date strings that might be in ISO format
  String _formatDateString(String dateStr) {
    if (dateStr.isEmpty) return '';

    try {
      // Check if the date contains 'T' which indicates ISO format with time
      if (dateStr.contains('T')) {
        final DateTime parsedDate = DateTime.parse(dateStr);
        return _dateFormat.format(parsedDate);
      }
      return dateStr;
    } catch (e) {
      print("Error formatting date: $e");
      return dateStr;
    }
  }

  Future<void> _loadUserProfile() async {
    setState(() {
      isLoading = true;
    });

    try {
      final userProfile = await UserPreferencesUtils.getUserProfile();

      // Format date of birth if it exists and is in ISO format
      String formattedDob = '';
      if (userProfile['dob'] != null && userProfile['dob'].isNotEmpty) {
        formattedDob = _formatDateString(userProfile['dob']);
      }

      setState(() {
        userData = {
          'email': userProfile['email'] ?? '',
          'username': userProfile['name'] ?? '',
          'dateOfBirth': formattedDob,
          'address': userProfile['address'] ?? '',
          'distance': userProfile['searchDistance'] ?? 1.0,
        };
      });
    } catch (error) {
      print("Error loading profile: $error");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load profile. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  void _showEditDialog() {
    if (editField == 'dateOfBirth') {
      _showDatePickerDialog();
    } else {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Edit ${editField.capitalize()}'),
          content: Form(
            key: _formKey,
            child: TextFormField(
              controller: _editController,
              decoration: InputDecoration(
                labelText: 'Enter new ${editField.capitalize()}',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  editValue = value;
                });
              },
              keyboardType: editField == 'distance'
                  ? TextInputType.numberWithOptions(decimal: true)
                  : TextInputType.text,
              validator: (value) => validateField(value ?? ''),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('Cancel'),
            ),
            TextButton(
              onPressed: isUpdating
                  ? null
                  : () {
                if (_formKey.currentState?.validate() == true) {
                  Navigator.of(context).pop();
                  saveEdit();
                }
              },
              child: isUpdating
                  ? SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                ),
              )
                  : Text('Save'),
            ),
          ],
        ),
      );
    }
  }

  void _showDatePickerDialog() {
    DateTime initialDate;
    try {
      initialDate = userData['dateOfBirth'].isNotEmpty
          ? _dateFormat.parse(userData['dateOfBirth'])
          : DateTime.now().subtract(Duration(days: 365 * 18)); // Default to 18 years ago
    } catch (e) {
      initialDate = DateTime.now().subtract(Duration(days: 365 * 18));
    }

    showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    ).then((pickedDate) {
      if (pickedDate != null) {
        setState(() {
          editValue = _dateFormat.format(pickedDate);
        });
        saveEdit();
      }
    });
  }

  void handleEdit(String field) {
    _editController.text = field == "distance"
        ? userData['distance'].toString()
        : userData[field] ?? '';

    setState(() {
      editField = field;
      editValue = _editController.text;
    });

    _showEditDialog();
  }

  String? validateField(String value) {
    if (editField == 'email') {
      // Email validation
      final emailRegExp = RegExp(r'^[a-zA-Z0-9.]+@[a-zA-Z0-9]+\.[a-zA-Z]+');
      if (!emailRegExp.hasMatch(value)) {
        return 'Please enter a valid email address';
      }
    } else if (editField == 'name') {
      // Username validation
      if (value.isEmpty) {
        return 'Username cannot be empty';
      } else if (value.length < 3) {
        return 'Username must be at least 3 characters';
      }
    } else if (editField == 'dob') {
      // Basic date validation (you can use more complex validation)
      final dateRegExp = RegExp(r'^\d{4}-\d{2}-\d{2}$');
      if (!dateRegExp.hasMatch(value) && value.isNotEmpty) {
        return 'Date format should be YYYY-MM-DD';
      }
    } else if (editField == 'distance') {
      try {
        double distance = double.parse(value);
        if (distance < 0.1 || distance > 10) {
          return 'Distance must be between 0.1 and 10 km';
        }
      } catch (e) {
        return 'Please enter a valid number';
      }
    }
    return null;
  }

  Future<void> saveEdit() async {
    if (editField != 'dateOfBirth' && _formKey.currentState?.validate() != true) {
      return;
    }

    setState(() {
      isUpdating = true;
    });

    try {
      bool success = false;
      Map<String, dynamic> apiUpdateData = {};

      // First update in SharedPreferences
      if (editField == 'username') {
        await UserPreferencesUtils.updateUserProfileField(
            UserPreferencesUtils.USER_NAME_KEY, editValue);
        apiUpdateData['name'] = editValue;
        success = true;
      } else if (editField == 'email') {
        await UserPreferencesUtils.updateUserProfileField(
            UserPreferencesUtils.USER_EMAIL_KEY, editValue);
        apiUpdateData['email'] = editValue;
        success = true;
      } else if (editField == 'dateOfBirth') {
        await UserPreferencesUtils.updateDateOfBirth(editValue);
        // Format for API: Use the same format as it's stored in database (YYYY-MM-DD)
        apiUpdateData['date_of_birth'] = editValue;
        success = true;
      } else if (editField == 'address') {
        await UserPreferencesUtils.updateUserProfileField(
            UserPreferencesUtils.USER_ADDRESS_KEY, editValue);
        apiUpdateData['address'] = editValue;
        success = true;
      } else if (editField == 'distance') {
        double distance = double.parse(editValue);
        await UserPreferencesUtils.updateSearchDistance(distance);
        apiUpdateData['search_distance'] = distance;
        success = true;
      }

      // Then update in database via API
      if (success && apiUpdateData.isNotEmpty) {
        await ProfileApi.updateProfile(apiUpdateData);

        setState(() {
          if (editField == "distance") {
            userData[editField] = double.parse(editValue);
          } else {
            userData[editField] = editValue;
          }
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Profile updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update profile'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (error) {
      print("Update error: $error");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error updating profile: ${error.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        isUpdating = false;
      });
    }
  }

  Future<void> handleLogout() async {
    if (isLoggingOut) return;

    setState(() {
      isLoggingOut = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      // Still using AuthProvider for logout as it may handle additional logout logic
      final success = await authProvider.logout();

      if (success) {
        // Clear local storage data
        await UserPreferencesUtils.clearUserProfile();
        Navigator.of(context).pushReplacementNamed('/welcome');
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to log out. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (error) {
      print("Logout error: $error");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error logging out: ${error.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        isLoggingOut = false;
      });
    }
  }

  Widget renderField(String label, String field, String value, Widget icon) {
    return Card(
      margin: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: ListTile(
        leading: icon,
        title: Text(label),
        subtitle: Text(value.isNotEmpty ? value : 'Not set'),
        trailing: IconButton(
          icon: Icon(Icons.edit),
          onPressed: () => handleEdit(field),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        // Add a leading back button
        title: Text('My Profile'),
        actions: [
          IconButton(
            icon: Icon(Icons.logout),
            onPressed: isLoggingOut ? null : handleLogout,
          ),
        ],
      ),
      body: isLoading
          ? Center(child: CircularProgressIndicator())
          : RefreshIndicator(
        onRefresh: _loadUserProfile,
        child: ListView(
          padding: EdgeInsets.all(16),
          children: [
            SizedBox(height: 20),
            Center(
              child: CircleAvatar(
                radius: 50,
                backgroundColor: Theme.of(context).primaryColor,
                child: Text(
                  userData['username']?.isNotEmpty == true
                      ? userData['username']![0].toUpperCase()
                      : '?',
                  style: TextStyle(fontSize: 40, color: Colors.white),
                ),
              ),
            ),
            SizedBox(height: 30),
            renderField(
              'Username',
              'username',
              userData['username'] ?? '',
              Icon(Icons.person),
            ),
            renderField(
              'Email',
              'email',
              userData['email'] ?? '',
              Icon(Icons.email),
            ),
            renderField(
              'Date of Birth',
              'dateOfBirth',
              userData['dateOfBirth'] ?? '',
              Icon(Icons.calendar_today),
            ),
            renderField(
              'Address',
              'address',
              userData['address'] ?? '',
              Icon(Icons.location_on),
            ),
            renderField(
              'Search Distance (km)',
              'distance',
              userData['distance']?.toString() ?? '1.0',
              Icon(Icons.social_distance),
            ),
          ],
        ),
      ),
    );
  }
}

// Helper extension to capitalize strings
extension StringExtension on String {
  String capitalize() {
    return this.isEmpty ? '' : '${this[0].toUpperCase()}${this.substring(1)}';
  }
}