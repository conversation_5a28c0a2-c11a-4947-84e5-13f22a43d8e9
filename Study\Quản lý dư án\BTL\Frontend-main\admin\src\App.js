import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Navbar from './components/Navbar';
import Sidebar from './components/Sidebar';
import Dashboard from './components/Dashboard';
import DataCrawler from './components/DataCrawler';
import DataUpdater from './components/DataUpdater';
import PostList from './components/PostList';
import { AppProvider } from './context/AppContext';

function App() {
  return (
    <AppProvider>
      <Router>
        <div className="flex h-screen bg-gray-100">
          <Sidebar />
          <div className="flex-1 flex flex-col overflow-hidden">
            <Navbar />
            <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-200 p-6">
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/crawl" element={<DataCrawler />} />
                <Route path="/update" element={<DataUpdater />} />
                <Route path="/posts" element={<PostList />} />
              </Routes>
            </main>
          </div>
        </div>
      </Router>
    </AppProvider>
  );
}

export default App;