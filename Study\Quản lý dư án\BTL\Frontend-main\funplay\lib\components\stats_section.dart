import 'package:flutter/material.dart';
import 'package:funplay/models/post_detail.dart';
import 'package:funplay/constants/colors.dart';
import 'package:funplay/constants/images.dart';

class StatsSection extends StatelessWidget {
  final PostDetail postDetail;

  const StatsSection({Key? key, required this.postDetail}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final commentsCount = postDetail.comments;
    final sharesCount = postDetail.sharesCount;
    final reactionsCount = postDetail.reactions;
    final rating = postDetail.rate;

    // Count valid images
    final imageUrls = postDetail.getImageUrls();
    final picturesCount = imageUrls.length;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 10),
      color: AppColors.white,
      child: Row(
        children: [
          // Comments
          _buildStatItem('Comments', commentsCount.toString()),

          // Pictures
          _buildStatItem('Pictures', picturesCount.toString()),

          // Shares
          _buildStatItem('Shares', sharesCount.toString()),

          // Likes/Reactions
          _buildStatItem('Likes', reactionsCount.toString()),

          // Rating
          Expanded(
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.star,
                      size: 16,
                      color: const Color(0xFFFFD700),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      rating.toStringAsFixed(1),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.black,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  'Rating',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Expanded(
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.black,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}