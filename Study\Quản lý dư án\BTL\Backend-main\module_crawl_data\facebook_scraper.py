import os
import requests
import json
import time
import re
from datetime import datetime, timedelta
from database import DatabaseManager

class FacebookScraper:
    def __init__(self, cookie, page_or_group_url, mysql_config, limit=10, max_posts=50, sleep=2, data_folder='data'):
        self.cookie = cookie
        self.page_or_group_url = page_or_group_url
        self.limit = limit
        self.max_posts = max_posts
        self.sleep = sleep
        self.data_folder = data_folder
        self.session = requests.Session()
        
        self.mysql_config = mysql_config
        
        self.db_manager = DatabaseManager(mysql_config)
        
        self.post_fields = 'id,parent_id,created_time,permalink_url,full_picture,shares,reactions.summary(total_count),attachments{subattachments.limit(20)},message'
        self.comment_fields = 'comments.order(chronological).summary(total_count){id,created_time,reactions.summary(total_count),message,comment_count,comments}'
        self.endpoint, self.access_token = self.init_params()

        # Ensure the data folder exists
        os.makedirs(self.data_folder, exist_ok=True)
        
    def get_node_id(self):
        node_type, node_name = self.page_or_group_url.split('/')[-2:]
        if node_type != 'groups': 
            return node_name
        
        id_in_url = re.search(r'(?<=/groups/)(\d+)', self.page_or_group_url)
        if id_in_url:
            return id_in_url.group(1)
        
        print('Getting Group ID ...')
        response = self.session.get(self.page_or_group_url)
        search_group_id = re.search(r'(?<=/group/\?id=)(\d+)', response.text)
        if search_group_id:
            group_id = search_group_id.group(1)
            print(f'Group ID for {node_name} is {group_id} !!')
            return group_id
        
        print('Cannot find any Node ID for', self.page_or_group_url)
        return None

    def get_access_token(self):
        print('Getting access token ...')
        response = self.session.get('https://business.facebook.com/business_locations', headers={'cookie': self.cookie})
        if response.status_code == 200:
            search_token = re.search(r'(EAAG\w+)', response.text)
            if search_token:
                return search_token.group(1)
        
        print('Cannot find access token. Maybe your cookie is invalid !!')
        return None

    def init_params(self):
        node_id = self.get_node_id()
        access_token = self.get_access_token()
        fields = self.post_fields + ',' + self.comment_fields
        endpoint = f'https://graph.facebook.com/v18.0/{node_id}/feed?limit={self.limit}&fields={fields}&access_token={access_token}'
        return endpoint, access_token

    def get_data_and_next_endpoint(self, endpoint):
        if not self.access_token:
            return {}, None

        response = self.session.get(endpoint, headers={'cookie': self.cookie})
        response = json.loads(response.text)

        try:
            data = response['data']
        except KeyError:
            print('\n', response.get('error', {}).get('message', 'Unknown error'))
            data = []

        try:
            next_endpoint = response['paging']['next']
            time.sleep(self.sleep)
        except KeyError:
            print('\nCannot find next endpoint')
            next_endpoint = None
        
        if next_endpoint and not next_endpoint.split('/feed?')[-1].startswith(f'limit={self.limit}&'):
            next_endpoint = next_endpoint.replace('/feed?', f'/feed?limit={self.limit}&')
        return data, next_endpoint

    @staticmethod
    def remove_paging(obj):
        if isinstance(obj, dict):
            return {k: FacebookScraper.remove_paging(v) for k, v in obj.items() if k != 'paging'}
        elif isinstance(obj, list):
            return [FacebookScraper.remove_paging(item) for item in obj]
        return obj
    
    def _get_clean_file_identifier(self):
        url_without_params = self.page_or_group_url.split('?')[0]

        identifier = url_without_params.strip('/').split('/')[-1]

        if not identifier or re.search(r'[^\w-]', identifier):
            node_id = self.get_node_id()
            identifier = node_id if node_id else 'unknown_group'

        identifier = re.sub(r'[^\w-]', '', identifier)
        
        return identifier
    
    def scrape_posts(self):
        if not self.endpoint or not self.access_token:
            print("Initialization failed. Cannot proceed.")
            return
        if not self.db_manager.create_all_tables():
            print("Failed to create tables")
            return
        
        file_identifier = self._get_clean_file_identifier()
        file_name = os.path.join(self.data_folder, f"{file_identifier}.json")
        count = 0
        print(f'Fetching {self.max_posts} posts sorted by RECENT_ACTIVITY from {self.page_or_group_url} ...')

        with open(file_name, 'w', encoding='utf-8') as file:
            while self.endpoint and count < self.max_posts:
                print(f'=> Number of posts now: {count} ...', end='\r', flush=True)
                data, self.endpoint = self.get_data_and_next_endpoint(self.endpoint)
                
                for post in data:
                    cleaned_post = self.remove_paging(post)

                    self.db_manager.save_all_data_to_mysql(cleaned_post)

                    file.write(json.dumps(cleaned_post, ensure_ascii=False) + '\n')
                    
                    count += 1
                
                if self.limit > self.max_posts - count:
                    self.endpoint = self.endpoint.replace(
                        f'/feed?limit={self.limit}&', 
                        f'/feed?limit={self.max_posts - count}&'
                    )
        
        print(f'\n=> Finish fetching {count} posts into {file_name} !!')
        self.session.close()