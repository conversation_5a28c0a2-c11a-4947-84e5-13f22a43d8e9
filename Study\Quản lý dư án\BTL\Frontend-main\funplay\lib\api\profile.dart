import 'dart:convert';
import 'package:http/http.dart' as http;
import './config.dart';
import '../utils/auth_utils.dart';

class ProfileApi {
  static Future<Map<String, dynamic>> getProfile() async {
    try {
      final token = await AuthUtils.getAuthToken();

      if (token == null) {
        throw Exception('No auth token available');
      }

      final response = await http.get(
        Uri.parse('${ApiConfig.apiBaseUrl}/profile'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 401 &&
          data['message']!.toString().contains('token failed')) {
        throw Exception('Not authorized, token failed');
      }

      if (response.statusCode == 200 && data['success']) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to get profile');
      }
    } catch (error) {
      print('Get profile error: $error');
      throw error;
    }
  }

  static Future<Map<String, dynamic>> updateProfile(Map<String, dynamic> profileData) async {
    try {
      final token = await AuthUtils.getAuthToken();

      if (token == null) {
        throw Exception('No auth token available');
      }

      // Map frontend field names to backend expected field names
      final Map<String, dynamic> apiData = {};

      // Check and map each field correctly
      if (profileData.containsKey('name')) {
        apiData['username'] = profileData['name'];
      }

      if (profileData.containsKey('date_of_birth')) {
        apiData['dateOfBirth'] = profileData['date_of_birth'];
      }

      // Pass other fields directly
      if (profileData.containsKey('email')) {
        apiData['email'] = profileData['email'];
      }

      if (profileData.containsKey('address')) {
        apiData['address'] = profileData['address'];
      }

      if (profileData.containsKey('search_distance')) {
        apiData['search_distance'] = profileData['search_distance'];
      }

      // Check if there are any fields to update
      if (apiData.isEmpty) {
        throw Exception('No fields to update');
      }

      final response = await http.put(
        Uri.parse('${ApiConfig.apiBaseUrl}/profile/update'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: jsonEncode(apiData),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 401 &&
          data['message']!.toString().contains('token failed')) {
        throw Exception('Not authorized, token failed');
      }

      if (response.statusCode == 200 && data['success']) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to update profile');
      }
    } catch (error) {
      print('Update profile error: $error');
      throw error;
    }
  }
}