import mysql.connector
from datetime import datetime, timedelta

class DatabaseManager:
    def __init__(self, mysql_config):
        self.mysql_config = mysql_config
        self.connection = self.create_mysql_connection()
        
    def create_mysql_connection(self):
        try:
            connection = mysql.connector.connect(**self.mysql_config)
            print("Connection successful")
            return connection
        except mysql.connector.Error as err:
            print(f"Error connecting to MySQL: {err}")
            return None
        
    def create_all_tables(self):
        if not self.connection:
            return False
        
        try:
            cursor = self.connection.cursor()
            
            # Create posts table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS posts (
                    id VARCHAR(50) PRIMARY KEY,
                    created_time DATETIME NOT NULL,
                    permalink_url VARCHAR(1000) NOT NULL,
                    full_picture VARCHAR(1000) NOT NULL,
                    shares_count INT NOT NULL DEFAULT 0,
                    reactions_total_count INT NOT NULL DEFAULT 0,
                    message TEXT NOT NULL,
                    status VARCHAR(20) NOT NULL DEFAULT 'neutral',
                    rate DOUBLE NOT NULL DEFAULT 0,
                    isTaggedSentiment TINYINT(1) DEFAULT 0,
                    address NVARCHAR(255) NOT NULL DEFAULT '',
                    isTaggedPosition TINYINT(1) NOT NULL DEFAULT 0,
                    latitude DOUBLE NOT NULL DEFAULT 0,
                    longitude DOUBLE NOT NULL DEFAULT 0
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """)
            
            # Create comments table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS comments (
                    id VARCHAR(50) PRIMARY KEY,
                    post_id VARCHAR(50),
                    created_time DATETIME,
                    message TEXT,
                    status VARCHAR(20) NOT NULL DEFAULT 'neutral',
                    rate DOUBLE NOT NULL DEFAULT 0,
                    reactions_total_count INT DEFAULT 0,
                    comment_count INT DEFAULT 0,
                    isTaggedSentiment TINYINT(1) DEFAULT 0,
                    FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """)
            
            # Create sub_comments table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS sub_comments (
                    id VARCHAR(50) PRIMARY KEY,
                    parent_comment_id VARCHAR(50),
                    created_time DATETIME,
                    message TEXT,
                    status VARCHAR(20) NOT NULL DEFAULT 'neutral',
                    rate DOUBLE NOT NULL DEFAULT 0,
                    isTaggedSentiment TINYINT(1) DEFAULT 0,
                    FOREIGN KEY (parent_comment_id) REFERENCES comments(id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """)
            
            # Create post_images table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS post_images (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    post_id VARCHAR(50),
                    full_picture_src VARCHAR(1000),
                    FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """)
            
            # Create sub_post_images table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS sub_post_images (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    post_id VARCHAR(50),
                    image_height INT,
                    image_width INT,
                    image_src VARCHAR(1000),
                    target_id VARCHAR(50),
                    target_url VARCHAR(1000),
                    type VARCHAR(50),
                    FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """)
            
            self.connection.commit()
            cursor.close()
            return True
        except mysql.connector.Error as err:
            print(f"Error creating tables: {err}")
            return False

    def save_all_data_to_mysql(self, post):
        if not self.connection:
            return False
        
        try:
            cursor = self.connection.cursor(dictionary=True)  # Use dictionary cursor for easier data comparison

            post_id = post.get('id', '')
            created_time_str = post.get('created_time', '')

            if created_time_str:
                try:
                    created_time = datetime.strptime(created_time_str, '%Y-%m-%dT%H:%M:%S%z')
                    one_year_ago = datetime.now(created_time.tzinfo) - timedelta(days=365)
                    if created_time < one_year_ago:
                        cursor.close()
                        return False
                except ValueError:
                    cursor.close()
                    return False
            
            # Check if post exists and get current data
            check_sql = """
                SELECT id, created_time, permalink_url, full_picture, 
                    shares_count, reactions_total_count, message, status, rate 
                FROM posts WHERE id = %s
            """
            cursor.execute(check_sql, (post_id,))
            existing_post = cursor.fetchone()

            # Prepare new post data
            new_post_data = {
                'id': post_id,
                'created_time': post.get('created_time', ''),
                'permalink_url': post.get('permalink_url', ''),
                'full_picture': post.get('full_picture', ''),
                'shares_count': post.get('shares', {}).get('count', 0),
                'reactions_total_count': post.get('reactions', {}).get('summary', {}).get('total_count', 0),
                'message': post.get('message', ''),
                'status': 'neutral',
                'rate': 0.0
            }

            if existing_post:
                # Check if main post data has changed
                has_changes = False
                for key in new_post_data:
                    if str(new_post_data[key]) != str(existing_post[key]):
                        has_changes = True
                        break

                # Check for changes in images
                cursor.execute("SELECT full_picture_src FROM post_images WHERE post_id = %s", (post_id,))
                existing_images = cursor.fetchall()
                existing_image_urls = {img['full_picture_src'] for img in existing_images}
                new_image_url = post.get('full_picture', '')
                if new_image_url and new_image_url not in existing_image_urls:
                    has_changes = True

                # Check for changes in comments
                cursor.execute("SELECT id, message, reactions_total_count FROM comments WHERE post_id = %s", (post_id,))
                existing_comments = {comment['id']: comment for comment in cursor.fetchall()}
                new_comments = post.get('comments', {}).get('data', [])
                
                for comment in new_comments:
                    comment_id = comment.get('id')
                    if comment_id not in existing_comments:
                        has_changes = True
                        break
                    existing_comment = existing_comments[comment_id]
                    if (str(comment.get('message', '')) != str(existing_comment['message']) or
                        comment.get('reactions', {}).get('summary', {}).get('total_count', 0) != existing_comment['reactions_total_count']):
                        has_changes = True
                        break

                if not has_changes:
                    cursor.close()
                    return False

                # Update post data if changes found
                update_sql = """
                    UPDATE posts SET 
                    created_time = %(created_time)s,
                    permalink_url = %(permalink_url)s,
                    full_picture = %(full_picture)s,
                    shares_count = %(shares_count)s,
                    reactions_total_count = %(reactions_total_count)s,
                    message = %(message)s
                    WHERE id = %(id)s
                """
                cursor.execute(update_sql, new_post_data)
            else:
                # Insert new post if it doesn't exist
                post_sql = """
                    INSERT INTO posts 
                    (id, created_time, permalink_url, full_picture, 
                    shares_count, reactions_total_count, message, status, rate) 
                    VALUES (%(id)s, %(created_time)s, %(permalink_url)s, %(full_picture)s, 
                    %(shares_count)s, %(reactions_total_count)s, %(message)s, %(status)s, %(rate)s)
                """
                cursor.execute(post_sql, new_post_data)

            # Save/update images
            self._save_post_images(post_id, post, cursor)

            # Save/update comments
            comments = post.get('comments', {}).get('data', [])
            for comment in comments:
                self._save_comment(comment, post_id, cursor)

            self.connection.commit()
            cursor.close()
            return True
        except mysql.connector.Error as err:
            print(f"Error saving data to MySQL: {err}")
            return False

    def _save_post_images(self, post_id, post, cursor):
        try:
            # Save main image
            if post.get('full_picture', ''):
                image = post.get('full_picture', '')
                img_sql = """
                    INSERT INTO post_images 
                    (post_id, full_picture_src) 
                    VALUES (%s, %s)
                    ON DUPLICATE KEY UPDATE 
                    full_picture_src = VALUES(full_picture_src)
                """
                cursor.execute(img_sql, (post_id, image))

            attachments = post.get('attachments', {})
            if attachments and 'data' in attachments and attachments['data']:
                first_attachment = attachments['data'][0]
                subattachments = first_attachment.get('subattachments', {})
                
                if subattachments and 'data' in subattachments:
                    for sub_img in subattachments['data']:
                        if sub_img.get('media', {}).get('image'):
                            sub_image = sub_img['media']['image']
                            sub_img_sql = """
                                INSERT INTO sub_post_images 
                                (post_id, image_height, image_width, image_src, 
                                target_id, target_url, type) 
                                VALUES (%s, %s, %s, %s, %s, %s, %s)
                                ON DUPLICATE KEY UPDATE
                                image_src = VALUES(image_src),
                                image_height = VALUES(image_height),
                                image_width = VALUES(image_width)
                            """
                            cursor.execute(sub_img_sql, (
                                post_id, 
                                sub_image.get('height'), 
                                sub_image.get('width'), 
                                sub_image.get('src'),
                                sub_img.get('target', {}).get('id', ''),
                                sub_img.get('target', {}).get('url', ''),
                                sub_img.get('type', '')
                            ))
        except Exception as err:
            print(f"Error saving post images: {err}")

    def _save_comment(self, comment, post_id, cursor):
        try:
            # Save main comment
            comment_data = {
                'id': comment.get('id', ''),
                'post_id': post_id,
                'created_time': comment.get('created_time', ''),
                'message': comment.get('message', ''),
                'status': 'neutral',
                'reactions_total_count': comment.get('reactions', {}).get('summary', {}).get('total_count', 0),
                'comment_count': comment.get('comment_count', 0)
            }
            
            comment_sql = """
                INSERT INTO comments 
                (id, post_id, created_time, message, 
                status, reactions_total_count, comment_count) 
                VALUES (%(id)s, %(post_id)s, %(created_time)s, %(message)s, 
                %(status)s, %(reactions_total_count)s, %(comment_count)s)
                ON DUPLICATE KEY UPDATE 
                created_time = VALUES(created_time),
                message = VALUES(message),
                status = VALUES(status),
                reactions_total_count = VALUES(reactions_total_count),
                comment_count = VALUES(comment_count)
            """
            cursor.execute(comment_sql, comment_data)

            # Save sub-comments if they exist
            sub_comments = comment.get('comments', {}).get('data', [])
            for sub_comment in sub_comments:
                sub_comment_data = {
                    'id': sub_comment.get('id', ''),
                    'parent_comment_id': comment.get('id', ''),
                    'created_time': sub_comment.get('created_time', ''),
                    'message': sub_comment.get('message', ''),
                    'status': 'neutral'
                }
                
                sub_comment_sql = """
                    INSERT INTO sub_comments 
                    (id, parent_comment_id, created_time, message, status) 
                    VALUES (%(id)s, %(parent_comment_id)s, %(created_time)s, %(message)s, %(status)s)
                    ON DUPLICATE KEY UPDATE 
                    created_time = VALUES(created_time),
                    message = VALUES(message),
                    status = VALUES(status)
                """
                cursor.execute(sub_comment_sql, sub_comment_data)

        except Exception as err:
            print(f"Error saving comments: {err}")